import datetime
from collections import defaultdict
from logger import logger
from sql.db_pool import get_connection

# Import caching functionality
try:
    from backend.cache import cache_live_status, cache_board_data
    CACHING_AVAILABLE = True
except ImportError:
    logger.warning("Caching module not available, running without cache")
    CACHING_AVAILABLE = False

    # Create dummy decorators if caching is not available
    def cache_live_status(ttl_minutes=5):
        def decorator(func):
            return func
        return decorator

    def cache_board_data(ttl_minutes=10):
        def decorator(func):
            return func
        return decorator

async def execute_query(sql: str, params: tuple = None, fetch_all: bool = True):
    """
    Execute SQL query using asyncpg with proper connection pooling.
    使用 asyncpg 执行 SQL 查询，并使用连接池。
    
    Args:
        sql: SQL query string SQL 查询字符串
        params: Parameters for the query (tuple or dict) 查询参数（元组或字典）
        fetch_all: Whether to fetch all results (for SELECT) or just return rowcount
                   是否获取所有结果（针对 SELECT 查询），否则只返回受影响的行数
        
    Returns:
        For SELECT queries: List of dicts if fetch_all=True, single dict if fetch_all=False
        对于 SELECT 查询：如果 fetch_all=True，返回字典列表；如果 fetch_all=False，返回单个字典
        For DML queries: Number of affected rows
        对于 DML 查询：返回受影响的行数
    """
    # 使用全局连接池获取连接
    async with get_connection() as conn:
        try:
            if fetch_all:
                # 执行查询并获取所有结果
                results = await conn.fetch(sql, *params if params else ())
                return [dict(record) for record in results]
            else:
                # 执行查询并获取单行结果
                record = await conn.fetchrow(sql, *params if params else ())
                return dict(record) if record else None
        except Exception as e:
            logger.error(f"Error executing query: {sql}\nParams: {params}\nError: {str(e)}")
            raise

async def collect_whole_flow_from_all_vtubers(current_time_str: str, previous_time_str: str):
    """
    Refers to current_time, previous_time, collect where the target vtuber's dahanghai(uid) go: lost directly or from one vtuber to another vtuber
    :param current_time_str: str like '2022-01-01'
    :param previous_time_str: str like '2022-01-01'
    Output format is similar to analyze_user_flow in vup_total_board.py
    """
    # SQL to fetch dahanghai users for a given time
    # dahanghai_list_table columns: up_uid, up_name, time (DATE), uid (BIGINT, fan's uid), username, face
    dahanghai_users_sql = """
        SELECT up_uid, up_name, uid, username, face
        FROM dahanghai_list_table
        WHERE time::text = $1;
    """

    current_records = await execute_query(dahanghai_users_sql, (current_time_str,))
    previous_records = await execute_query(dahanghai_users_sql, (previous_time_str,))

    current_time_users_by_anchor = defaultdict(set)
    for rec in current_records:
        user_tuple = (rec['uid'], rec['username'], rec['face'])
        current_time_users_by_anchor[rec['up_name']].add(user_tuple)

    previous_time_users_by_anchor = defaultdict(set)
    for rec in previous_records:
        user_tuple = (rec['uid'], rec['username'], rec['face'])
        previous_time_users_by_anchor[rec['up_name']].add(user_tuple)

    direct_loss = defaultdict(set)
    p2c_flow_between_anchors = defaultdict(lambda: defaultdict(set)) # Previous_anchor -> Current_anchor -> Users
    
    all_current_users_set = set()
    for users_set in current_time_users_by_anchor.values():
        all_current_users_set.update(users_set)

    for prev_anchor_name, prev_users_set in previous_time_users_by_anchor.items():
        for user_tuple in prev_users_set:
            found_in_current = False
            # Check if user is with any anchor in current time
            for curr_anchor_name, curr_users_set in current_time_users_by_anchor.items():
                if user_tuple in curr_users_set:
                    if prev_anchor_name != curr_anchor_name:
                        p2c_flow_between_anchors[prev_anchor_name][curr_anchor_name].add(user_tuple)
                    found_in_current = True
                    break 
            if not found_in_current:
                direct_loss[prev_anchor_name].add(user_tuple)

    additional_users = defaultdict(set) # New users in current time not present in previous time for any anchor
    all_previous_users_set = set()
    for users_set in previous_time_users_by_anchor.values():
        all_previous_users_set.update(users_set)

    for curr_anchor_name, curr_users_set in current_time_users_by_anchor.items():
        for user_tuple in curr_users_set:
            if user_tuple not in all_previous_users_set:
                 additional_users[curr_anchor_name].add(user_tuple)
    
    # c2p_flow is essentially the inverse of p2c, or can be calculated similarly if needed from current's perspective
    # For simplicity, returning what's directly analogous to vup_total_board.py's analyze_user_flow
    # The original analyze_user_flow also calculates c2p_flow_between_anchors, which is current_anchor -> previous_anchor -> Users
    # This can be derived from p2c_flow_between_anchors or calculated by iterating current_time_users_by_anchor first.
    # Let's keep it simple and match the primary outputs.

    return {
        'loss': {k: list(v) for k, v in direct_loss.items()},
        'p2c': {k_outer: {k_inner: list(v_inner) for k_inner, v_inner in v_outer.items()} for k_outer, v_outer in p2c_flow_between_anchors.items()},
        'add': {k: list(v) for k, v in additional_users.items()},
    }


async def collect_target_flow_with_target_vtuber(target_vtube_name: str, current_time_str: str, previous_time_str: str, n: int = 5):
    """
    Refers to current_time, previous_time, collect how the target vtuber's dahanghai(uid) go to and come from other vtuber(top n)
    :param target_vtube_name: str, name of the target vTuber
    :param current_time_str: str like '2022-01-01'
    :param previous_time_str: str like '2022-01-01'
    :param n: int, top N flows
    """
    dahanghai_users_sql = """
        SELECT up_uid, up_name, uid, username, face
        FROM dahanghai_list_table
        WHERE time::text = $1;
    """
    
    current_records_all = await execute_query(dahanghai_users_sql, (current_time_str,))
    previous_records_all = await execute_query(dahanghai_users_sql, (previous_time_str,))

    # Users of the target vTuber
    target_current_users = set()
    for rec in current_records_all:
        if rec['up_name'] == target_vtube_name:
            target_current_users.add((rec['uid'], rec['username'], rec['face']))
            
    target_previous_users = set()
    for rec in previous_records_all:
        if rec['up_name'] == target_vtube_name:
            target_previous_users.add((rec['uid'], rec['username'], rec['face']))

    # Users who flowed IN to target
    flowed_in_users = target_current_users - target_previous_users
    flowed_in_sources = defaultdict(list)
    if flowed_in_users:
        for rec in previous_records_all:
            if rec['up_name'] != target_vtube_name: # Source must be other vTubers
                user_tuple = (rec['uid'], rec['username'], rec['face'])
                if user_tuple in flowed_in_users:
                    flowed_in_sources[rec['up_name']].append(user_tuple)
    
    # Users who flowed OUT from target
    flowed_out_users = target_previous_users - target_current_users
    flowed_out_targets = defaultdict(list)
    if flowed_out_users:
        for rec in current_records_all:
            if rec['up_name'] != target_vtube_name: # Target must be other vTubers
                user_tuple = (rec['uid'], rec['username'], rec['face'])
                if user_tuple in flowed_out_users:
                    flowed_out_targets[rec['up_name']].append(user_tuple)

    # Sort and get top N
    sorted_flowed_in_sources = sorted(flowed_in_sources.items(), key=lambda item: len(item[1]), reverse=True)
    sorted_flowed_out_targets = sorted(flowed_out_targets.items(), key=lambda item: len(item[1]), reverse=True)

    return {
        'in': {k: v for k, v in sorted_flowed_in_sources[:n]},
        'out': {k: v for k, v in sorted_flowed_out_targets[:n]},
    }

async def collect_target_flow_with_target_vtuber_by_period(
    target_vtube_name: str,
    start_time_str1: str,
    end_time_str1: str,
    start_time_str2: str,
    end_time_str2: str,
    n: int = 5
):
    """
    Refers to two periods, collect how the target vtuber's dahanghai(uid) go to and come from other vtuber(top n)
    根据两个时间段，收集目标 VTuber 的大航海用户流入和流出情况（前 N 名）
    :param target_vtube_name: str, name of the target vTuber 目标 VTuber 的名称
    :param start_time_str1: str, start date of the first period (YYYY-MM-DD) 第一个时间段的开始日期
    :param end_time_str1: str, end date of the first period (YYYY-MM-DD) 第一个时间段的结束日期
    :param start_time_str2: str, start date of the second period (YYYY-MM-DD) 第二个时间段的开始日期
    :param end_time_str2: str, end date of the second period (YYYY-MM-DD) 第二个时间段的结束日期
    :param n: int, top N flows 前 N 名的流动
    """
    dahanghai_users_sql_period = """
        SELECT up_uid, up_name, uid, username, face
        FROM dahanghai_list_table
        WHERE time BETWEEN $1 AND $2;
    """

    # Convert date strings to datetime.date objects
    # 将日期字符串转换为 datetime.date 对象
    start_date1 = datetime.datetime.strptime(start_time_str1, '%Y-%m-%d').date()
    end_date1 = datetime.datetime.strptime(end_time_str1, '%Y-%m-%d').date()
    start_date2 = datetime.datetime.strptime(start_time_str2, '%Y-%m-%d').date()
    end_date2 = datetime.datetime.strptime(end_time_str2, '%Y-%m-%d').date()

    # Fetch records for Period 1
    # 获取第一个时间段的记录
    records_period1 = await execute_query(dahanghai_users_sql_period, (start_date1, end_date1))
    # Fetch records for Period 2
    # 获取第二个时间段的记录
    records_period2 = await execute_query(dahanghai_users_sql_period, (start_date2, end_date2))

    # Group users by up_name for each period
    # 按主播名称分组每个时间段的用户
    users_by_anchor_period1 = defaultdict(set)
    for rec in records_period1:
        user_tuple = (rec['uid'], rec['username'], rec['face'])
        users_by_anchor_period1[rec['up_name']].add(user_tuple)

    users_by_anchor_period2 = defaultdict(set)
    for rec in records_period2:
        user_tuple = (rec['uid'], rec['username'], rec['face'])
        users_by_anchor_period2[rec['up_name']].add(user_tuple)

    # Get target VTuber's users in each period
    # 获取目标 VTuber 在每个时间段的用户
    target_users_period1 = users_by_anchor_period1.get(target_vtube_name, set())
    target_users_period2 = users_by_anchor_period2.get(target_vtube_name, set())

    # Calculate flowed out users (were with target in P1, not in P2)
    # 计算流出用户（在时间段1属于目标，但在时间段2不属于目标）
    flowed_out_users = target_users_period1 - target_users_period2
    flowed_out_targets = defaultdict(list) # Other_VTuber_name -> List[User_Tuple] 其他 VTuber 名称 -> 用户元组列表
    if flowed_out_users:
        for other_anchor_name, other_anchor_users_set in users_by_anchor_period2.items():
            if other_anchor_name != target_vtube_name:
                for user_tuple in flowed_out_users:
                    if user_tuple in other_anchor_users_set:
                        flowed_out_targets[other_anchor_name].append(user_tuple)

    # Calculate flowed in users (were with target in P2, not in P1)
    # 计算流入用户（在时间段2属于目标，但在时间段1不属于目标）
    flowed_in_users = target_users_period2 - target_users_period1
    flowed_in_sources = defaultdict(list) # Other_VTuber_name -> List[User_Tuple] 其他 VTuber 名称 -> 用户元组列表
    if flowed_in_users:
        for other_anchor_name, other_anchor_users_set in users_by_anchor_period1.items():
            if other_anchor_name != target_vtube_name:
                for user_tuple in flowed_in_users:
                    if user_tuple in other_anchor_users_set:
                        flowed_in_sources[other_anchor_name].append(user_tuple)

    # Sort and get top N
    # 排序并获取前 N 名
    sorted_flowed_in_sources = sorted(flowed_in_sources.items(), key=lambda item: len(item[1]), reverse=True)
    sorted_flowed_out_targets = sorted(flowed_out_targets.items(), key=lambda item: len(item[1]), reverse=True)

    return {
        'in': {k: v for k, v in sorted_flowed_in_sources[:n]},
        'out': {k: v for k, v in sorted_flowed_out_targets[:n]},
    }

async def collect_whole_info_from_all_vtubers():
    """
    Returns:
        {
            'time': str (current time of query)
            'data': [
                {
                    'name': str,
                    'face': str
                    'sign': str (description/bio)
                    'birthday': str
                    'cover_url': str (top_photo)
                },
                ...
            ]
        }
    """
    # user_info_table columns: uid, name, face, sign, birthday, top_photo, room_id, live_url
    sql = """
        SELECT uid, name, face, sign, birthday, top_photo AS cover_url
        FROM user_info_table;
    """
    records = await execute_query(sql)
    
    formatted_data = []
    for rec in records:
        formatted_data.append({
            'name': rec['name'],
            'uid': rec['uid'],
            'face': rec['face'],
            'sign': rec['sign'],
            'birthday': rec['birthday'],
            'cover_url': rec['cover_url']
        })
        
    return {
        'time': datetime.datetime.now().isoformat(),
        'data': formatted_data
    }

async def collect_all_dahanghai_and_follower_stuff():
    # desprecated
    """
    Returns:
        {
            'time': str (current time of query)
            'data': [
                {
                    'name': str,
                    'dahanghai_num': int
                    'dahanghai_rise_num': int
                    'follower_num': int
                    'follower_rise_num': int
                },
                ...
            ]
        }
    """
    # current_stat_table columns: uid, name, timestamp (bigint), datetime (timestamp), follower_num, dahanghai_num
    # Rise numbers are calculated by comparing with the previous record for each user.
    # dahanghai_rise_num/follower_rise_num is hours 级别
    sql = """
        WITH RankedStats AS (
            SELECT
                uid,
                name,
                timestamp, -- Order by this for LAG
                dahanghai_num,
                follower_num,
                LAG(dahanghai_num, 1, 0) OVER (PARTITION BY uid ORDER BY timestamp) as prev_dahanghai_num,
                LAG(follower_num, 1, 0) OVER (PARTITION BY uid ORDER BY timestamp) as prev_follower_num,
                ROW_NUMBER() OVER (PARTITION BY uid ORDER BY timestamp DESC) as rn
            FROM current_stat_table
        )
        SELECT
            uid,
            name,
            dahanghai_num,
            (dahanghai_num - prev_dahanghai_num) as dahanghai_rise_num,
            follower_num,
            (follower_num - prev_follower_num) as follower_rise_num
        FROM RankedStats
        WHERE rn = 1; -- Get the latest record for each user
    """
    records = await execute_query(sql)

    if isinstance(records, int):
        raise ValueError(f"Expected a list of records, but got an integer (row count). SQL: {sql}")
    
    formatted_data = []
    for rec in records:
        formatted_data.append({
            'name': rec['name'],
            'dahanghai_num': rec['dahanghai_num'],
            'dahanghai_rise_num': rec['dahanghai_rise_num'],
            'follower_num': rec['follower_num'],
            'follower_rise_num': rec['follower_rise_num']
        })

    return {
        'time': datetime.datetime.now().isoformat(),
        'data': formatted_data
    }

async def collect_all_dahanghai_and_follower_rise_num_stuff(recent_days:int = 1):
    """
    Returns:
        {
            'time': str (current time of query)
            'data': [
                {
                    'name': str,
                    'dahanghai_num': int
                    'dahanghai_rise_num': int
                    'follower_num': int
                    'follower_rise_num': int
                },
                ...
            ]
        }
    """
    # current_stat_table columns: uid, name, timestamp (bigint), datetime (timestamp with time zone), follower_num, dahanghai_num
    # dahanghai_rise_num/follower_rise_num are calculated by comparing the latest record 
    # with the record from 'recent_days' ago.
    # If no record is found 'recent_days' ago, the rise is calculated against null (i.e., current value is the rise if past is null).
    sql = """
        WITH LatestRecord AS (
            -- 获取每个用户的最新记录
            SELECT
                uid,
                name,
                datetime, -- 使用 datetime 列进行日期比较
                dahanghai_num,
                follower_num,
                ROW_NUMBER() OVER (PARTITION BY uid ORDER BY datetime DESC) as rn
            FROM current_stat_table
        ),
        CurrentData AS (
            SELECT 
                uid,
                name,
                datetime,
                dahanghai_num,
                follower_num
            FROM LatestRecord
            WHERE rn = 1
        ),
        PastRecord AS (
            -- 获取每个用户在 (最新记录时间 - recent_days) 或之前的最近记录
            -- 使用 $1 作为 recent_days 的占位符
            SELECT
                cs.uid,
                cs.datetime AS past_datetime,
                cs.dahanghai_num AS past_dahanghai_num_val,
                cs.follower_num AS past_follower_num_val,
                ROW_NUMBER() OVER (PARTITION BY cs.uid ORDER BY cs.datetime DESC) as rn_past
            FROM current_stat_table cs
            JOIN CurrentData cd ON cs.uid = cd.uid
            WHERE cs.datetime <= (cd.datetime - MAKE_INTERVAL(days => $1))
        ),
        PastData AS (
            SELECT
                uid,
                past_dahanghai_num_val,
                past_follower_num_val
            FROM PastRecord
            WHERE rn_past = 1
        )
        SELECT
            cd.name,
            cd.dahanghai_num,
            COALESCE(cd.dahanghai_num - pd.past_dahanghai_num_val, cd.dahanghai_num) AS dahanghai_rise_num,
            cd.follower_num,
            COALESCE(cd.follower_num - pd.past_follower_num_val, cd.follower_num) AS follower_rise_num
        FROM CurrentData cd
        LEFT JOIN PastData pd ON cd.uid = pd.uid;
    """
    records = await execute_query(sql, (recent_days,))
    
    formatted_data = []
    for rec in records:
        formatted_data.append({
            'name': rec['name'],
            'dahanghai_num': rec['dahanghai_num'],
            'dahanghai_rise_num': rec['dahanghai_rise_num'],
            'follower_num': rec['follower_num'],
            'follower_rise_num': rec['follower_rise_num']
        })

    return {
        'time': datetime.datetime.now().isoformat(),
        'data': formatted_data
    }

@cache_live_status(ttl_minutes=5)
async def collect_all_live_status_stuff():
    """
    Returns:
    [
        {
            'name': 'char',
            'if_live': True,
            'live_url': 'https://live.bilibili.com/123456',
            'live_title': 'title',
            'live_cover': 'https://i0.hdslb.com/bfs/live/cover.jpg',
            'live_roomid': '123456',
        },
        ...
    ]
    """
    import time

    # Optimized query using DISTINCT ON instead of window function
    # This is much more efficient and uses the new indexes
    sql = """
        SELECT DISTINCT ON (lsm.room_id)
            ui.name,
            CASE WHEN lsm.live_status = 1 THEN TRUE ELSE FALSE END AS if_live,
            ui.live_url,
            lsm.title AS live_title,
            lsm.cover AS live_cover,
            lsm.room_id AS live_roomid
        FROM live_status_minute_table lsm
        JOIN user_info_table ui ON lsm.room_id = ui.room_id
        WHERE lsm.datetime >= NOW() - INTERVAL '24 HOURS'
        ORDER BY lsm.room_id, lsm.datetime DESC;
    """

    start_time = time.time()
    records = await execute_query(sql)
    query_time = time.time() - start_time

    logger.info(f"Live status query completed in {query_time:.3f}s, returned {len(records) if records else 0} records")
    
    formatted_data = []
    for rec in records:
        formatted_data.append({
            'name': rec['name'],
            'if_live': rec['if_live'],
            'live_url': rec['live_url'],
            'live_title': rec['live_title'],
            'live_cover': rec['live_cover'],
            'live_roomid': rec['live_roomid']
        })
        
    return formatted_data

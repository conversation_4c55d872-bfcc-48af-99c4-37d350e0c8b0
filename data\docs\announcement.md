# 网站公告

## 🎉 Vupbi V2.0版本上线！

**发布时间：** 2024年5月27日

### 主要更新

全部更新内容可见链接：☞[虚拟偶像数据看板vupbi v2版本更新日志](https://doc.weixin.qq.com/doc/w3_AR0ACAY3ADcCNuceZg9BoQTiY4mz0?scode=AJEAIQdfAAosGc9cHRAR0ACAY3ADc)

* **后端架构优化：** 后端重构了用户信息的全部API，多线程处理提高了系统性能。
* **数据获取与存储改进：** 优化了调度器功能；将拉取数据迁移至外部服务器；处理了多种数据拉取和存储异常情况。
* **数据库引入：** 引入了postgres和异步连接池，并优化了多处查询逻辑，如将多个数据获取函数改为关联表查询，显著提升了数据库操作效率。
* **舰长流向分析优化：** 增加了时间范围筛选功能，允许用户自定义时间段进行分析。
* **直播分析数据维护：** 直播分析页面目前单独维护数据，包括存储轮播主播的数据。
* **白名单支持：** 集成了企业微信登录方式，提升了安全性。
* **前端面板优化：** 优化了一些数据的显示形式。
* **新增公告页面：** 方便查看网站更新日志和重要通知。
* **多项测试与归档完成：** 完成了大量后端接口和查询功能的测试，确保了数据准确性和系统稳定性；对多个数据表结构和数据获取逻辑进行了归档整理和优化，提升了系统的可维护性。
* **修复了用户反馈的一些已知BUG。**

### 后续计划

* **视频分析功能：** 补充星瞳的后台数据，包括视频播放量变化、封面点击率、完播率、单个视频转粉、播粉比和转粉率等情况。
* **尝试重新计算评论情感（sentiment）数据。**
* **开发Copilot界面：** 能直接沟通数据库，如"6月4日，星瞳的最近活动有哪些？"。

---

## 🚀 Vupbi V1.1正式发布！

**发布时间：** 2024年2月13日

欢迎使用Vupbi！我们致力于为用户提供全面的Vup数据分析服务。

### 目前已上线功能

* **个人空间：** 查看VTuber详细数据。
* **动态分析：** 追踪VTuber动态更新。
* **视频分析：** 分析VTuber视频表现。
* **评论分析：** 洞察观众评论情感。
* **直播分析：** 监控直播数据。
* **VUP对比：** 横向对比不同VTuber。
* **舰长流向：** 分析用户订阅行为。

感谢您的支持！

---

*最后更新：2024年5月27日*
